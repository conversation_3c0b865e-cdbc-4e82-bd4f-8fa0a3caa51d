package com.example.pure.exception;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * @RestControllerAdvice用于Spring自动注册
 * Spring 会自动捕获应用中抛出的异常并路由到对应的处理方法
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    /* 1.抛出异常首先对应new ExceptionHandler先用构造函数初始化变量，后在这里根据注解自动匹配ExceptionHandler然后引入，并完成对应的方法
       2.可以 @ExceptionHandler自动捕获其他类代码收到的trow或者throws的代码，并自动运行对应ExceptionHandler的方法。
       3.抛出异常和直接返回错误的结果，结果是一样的，不过前者能处理更多逻辑，选前者 */

    /**
     * 处理参数校验异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        log.warn("参数校验失败: {}", errors);
        return Result.error(ResponseCode.INVALID_PARAMETER, "参数校验失败", errors);
    }

    /**
     * 处理 Bean Validation 异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<?> handleConstraintViolationException(ConstraintViolationException ex) {
        String message = ex.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数校验失败: {}", message);
        return Result.error(ResponseCode.INVALID_PARAMETER, message);
    }

    /**
     * 处理绑定异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BindException.class)
    public Result<?> handleBindException(BindException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        log.warn("参数绑定失败: {}", errors);
        return Result.error(ResponseCode.INVALID_PARAMETER, "参数绑定失败", errors);
    }

    /**
     * 处理认证异常
     */
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(AuthenticationException.class)
    public Result<?> handleAuthenticationException(AuthenticationException ex) {
        log.warn("认证失败: {}", ex.getMessage());
        if (ex instanceof BadCredentialsException) {
            return Result.error(ResponseCode.INVALID_CREDENTIALS, "用户名或密码错误");
        }
        return Result.error(ResponseCode.UNAUTHORIZED, "认证失败");
    }

    /**
     * 处理授权异常
     */
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(AccessDeniedException.class)
    public Result<?> handleAccessDeniedException(AccessDeniedException ex) {
        log.warn("访问被拒绝: {}", ex.getMessage());
        return Result.error(ResponseCode.FORBIDDEN, "没有权限访问此资源");
    }

    /**
     * 处理业务异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException ex) {
        log.warn("业务异常: {}", ex.getMessage());
        if (ex.getData() != null) {
            return Result.error(ex.getCode(), ex.getMessage(), ex.getData());
        }
        return Result.error(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理客户端中断连接异常
     * <p>
     * 当客户端主动中断连接时触发，如用户关闭页面、视频播放器跳转或停止播放等。
     * 这种情况下不需要返回错误响应，因为客户端已经不再等待响应。
     * </p>
     *
     * @param ex 客户端中断连接异常
     */
    @ExceptionHandler(ClientAbortException.class)
    public ResponseEntity<Void> handleClientAbortException(ClientAbortException ex) {
        // 记录警告日志而非错误或调试，因为这是预期的客户端行为但仍需记录
        log.warn("客户端中断连接: {}", ex.getMessage());
        // 返回499状态码，但不含任何内容
        return ResponseEntity.status(ResponseCode.CLIENT_CLOSED_REQUEST.getCode()).build();
    }

    /**
     * 处理IO异常
     * <p>
     * 处理文件操作相关的IO异常
     * </p>
     */
    @ExceptionHandler(IOException.class)
    public ResponseEntity<?> handleIOException(IOException ex) {
        log.error("IO异常", ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.error(ResponseCode.INTERNAL_SERVER_ERROR, "文件处理失败: " + ex.getMessage()));
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handleException(Exception ex) {
        // 其他未知异常按原方式处理
        log.error("服务器处理API出现异常", ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Result.error(ResponseCode.INTERNAL_SERVER_ERROR, "服务器处理API出现异常，请联系管理员"));
    }

    /**
     * 处理JWT认证异常
     */
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(JwtAuthenticationException.class)
    public Result<?> handleJwtAuthenticationException(JwtAuthenticationException ex) {
        log.warn("JWT认证失败: {}", ex.getMessage());
        return Result.error(ResponseCode.UNAUTHORIZED, ex.getMessage());
    }

    /**
     * 处理访问频率限制异常
     */
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    @ExceptionHandler(RateLimitExceededException.class)
    public Result<?> handleRateLimitExceededException(RateLimitExceededException ex) {
        log.warn("访问频率超限: {}", ex.getMessage());
        return Result.error(429, ex.getMessage());
    }

}

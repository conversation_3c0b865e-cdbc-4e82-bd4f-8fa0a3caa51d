package com.example.pure.controller.openai;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.Result;
import com.example.pure.model.dto.request.log.RequestLogQueryRequest;
import com.example.pure.model.dto.response.openai.OpenAiRequestLogResponse;
import com.example.pure.service.openai.OpenAiRequestLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * OpenAI请求日志控制器
 * <p>
 * 提供OpenAI请求日志的查询和管理功能
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/logs/openai-requests")
@RequiredArgsConstructor
public class OpenAiRequestLogController {

    private final OpenAiRequestLogService openaiRequestLogService;

    /**
     * 分页查询OpenAI请求日志
     *
     * @param queryRequest 查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<PageFinalResult<OpenAiRequestLogResponse>> getOpenaiRequestLogsPage(
            @Valid RequestLogQueryRequest queryRequest) {

        log.info("收到分页查询OpenAI请求日志请求 - 页码: {}, 大小: {}", queryRequest.getPage(), queryRequest.getSize());

        try {
            PageFinalResult<OpenAiRequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询OpenAI请求日志失败", e);
            return Result.errorTyped(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据请求ID查询单个日志详情
     *
     * @param requestId 请求ID
     * @return 日志详情
     */
    @GetMapping("/{requestId}")
    public Result<OpenAiRequestLogResponse> getOpenaiRequestLogDetail(@PathVariable String requestId) {
        log.info("查询OpenAI请求日志详情 - 请求ID: {}", requestId);

        try {
            OpenAiRequestLogResponse response = openaiRequestLogService.getRequestLogByRequestId(requestId);
            if (response == null) {
                return Result.errorTyped(404, "请求日志不存在");
            }
            return Result.success("查询成功", response);
        } catch (Exception e) {
            log.error("查询OpenAI请求日志详情失败 - 请求ID: {}", requestId, e);
            return Result.errorTyped(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询失败的OpenAI请求日志
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID（可选）
     * @return 分页结果
     */
    @GetMapping("/failed")
    public Result<PageFinalResult<OpenAiRequestLogResponse>> getFailedOpenaiRequestLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long userId) {

        log.info("查询失败OpenAI请求日志 - 页码: {}, 大小: {}, 用户ID: {}", page, size, userId);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setUserId(userId);
            queryRequest.setOnlyFailed(true);

            PageFinalResult<OpenAiRequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return Result.success("查询失败日志成功", result);
        } catch (Exception e) {
            log.error("查询失败OpenAI请求日志失败", e);
            return Result.errorTyped(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询慢OpenAI请求日志（耗时超过指定时间）
     *
     * @param page 页码
     * @param size 每页大小
     * @param minDuration 最小耗时（毫秒，默认5000ms）
     * @param userId 用户ID（可选）
     * @return 分页结果
     */
    @GetMapping("/slow")
    public Result<PageFinalResult<OpenAiRequestLogResponse>> getSlowOpenaiRequestLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(defaultValue = "5000") Long minDuration,
            @RequestParam(required = false) Long userId) {

        log.info("查询慢OpenAI请求日志 - 页码: {}, 大小: {}, 最小耗时: {}ms, 用户ID: {}", page, size, minDuration, userId);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setUserId(userId);
            queryRequest.setMinDuration(minDuration);
            queryRequest.setOnlySlow(true);

            PageFinalResult<OpenAiRequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return Result.success("查询慢请求日志成功", result);
        } catch (Exception e) {
            log.error("查询慢OpenAI请求日志失败", e);
            return Result.errorTyped(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据提供商查询OpenAI请求日志
     *
     * @param provider 提供商
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    @GetMapping("/provider/{provider}")
    public Result<PageFinalResult<OpenAiRequestLogResponse>> getOpenaiRequestLogsByProvider(
            @PathVariable String provider,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        log.info("根据提供商查询OpenAI请求日志 - 提供商: {}, 页码: {}, 大小: {}", provider, page, size);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setProvider(provider);

            PageFinalResult<OpenAiRequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return Result.success("根据提供商查询成功", result);
        } catch (Exception e) {
            log.error("根据提供商查询OpenAI请求日志失败 - 提供商: {}", provider, e);
            return Result.errorTyped(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据模型名称查询OpenAI请求日志
     *
     * @param modelName 模型名称
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    @GetMapping("/model/{modelName}")
    public Result<PageFinalResult<OpenAiRequestLogResponse>> getOpenaiRequestLogsByModel(
            @PathVariable String modelName,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        log.info("根据模型名称查询OpenAI请求日志 - 模型: {}, 页码: {}, 大小: {}", modelName, page, size);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setModelName(modelName);

            PageFinalResult<OpenAiRequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return Result.success("根据模型查询成功", result);
        } catch (Exception e) {
            log.error("根据模型名称查询OpenAI请求日志失败 - 模型: {}", modelName, e);
            return Result.errorTyped(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除OpenAI请求日志
     *
     * @param id 日志ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteOpenaiRequestLog(@PathVariable Long id) {
        log.info("收到删除OpenAI请求日志请求 - 日志ID: {}", id);

        try {
            boolean success = openaiRequestLogService.deleteRequestLogById(id);
            if (success) {
                log.info("成功删除OpenAI请求日志 - 日志ID: {}", id);
                return Result.success("删除成功", "删除成功");
            } else {
                log.warn("删除OpenAI请求日志失败，日志不存在 - 日志ID: {}", id);
                return Result.errorTyped(404, "日志不存在");
            }
        } catch (Exception e) {
            log.error("删除OpenAI请求日志异常 - 日志ID: {}", id, e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }
}

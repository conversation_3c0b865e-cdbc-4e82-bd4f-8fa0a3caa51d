package com.example.pure.constant;

/**
 * 响应状态码枚举类
 *
 * <p>定义了系统中所有的响应状态码，遵循HTTP状态码的基本原则：
 * <ul>
 *     <li>2xx：成功</li>
 *     <li>4xx：客户端错误</li>
 *     <li>5xx：服务器错误</li>
 *     <li>6xx：自定义业务错误</li>
 * </ul>
 * </p>
 *
 * <p>使用示例：
 * <pre>{@code
 * // 在异常处理中使用
 * throw new BusinessException(ResponseCode.INVALID_PARAMETER.getCode(), ResponseCode.INVALID_PARAMETER.getMessage());
 *
 * // 在响应中使用
 * return Result.error(ResponseCode.UNAUTHORIZED);
 * }</pre>
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ResponseCode {

    // ========== 成功状态码 ==========
    /** 操作成功 */
    SUCCESS(200, "操作成功"),

    // ========== 客户端错误码 (400-499) ==========
    /** 错误的请求 */
    BAD_REQUEST(400, "错误的请求"),
    /** 未认证 */
    UNAUTHORIZED(401, "未认证"),
    /** 禁止访问 */
    FORBIDDEN(403, "禁止访问"),
    /** 资源不存在 */
    NOT_FOUND(404, "资源不存在"),
    /** 方法不允许 */
    METHOD_NOT_ALLOWED(405, "方法不允许"),
    /** 客户端关闭请求 */
    CLIENT_CLOSED_REQUEST(499, "客户端关闭请求"),

    // ========== 服务器错误码 (500-599) ==========
    /** 服务器内部错误 */
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    /** 服务不可用 */
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    /** 数据库错误 */
    DATABASE_ERROR(507, "数据库错误"),

    // ========== 业务错误码 (600+) ==========
    /** 通用业务错误 */
    BUSINESS_ERROR(600, "通用业务错误"),
    /** 参数验证错误 */
    INVALID_PARAMETER(601, "参数验证错误"),
    /** 唯一键冲突 */
    DUPLICATE_KEY(602, "唯一键冲突"),

    // ========== 用户相关错误码 (610-619) ==========
    /** 用户不存在 */
    USER_NOT_FOUND(610, "用户不存在"),
    /** 用户已存在 */
    USER_ALREADY_EXISTS(611, "用户已存在"),
    /** 密码错误 */
    INVALID_PASSWORD(612, "密码错误"),
    /** 账号已锁定 */
    ACCOUNT_LOCKED(613, "账号已锁定"),
    /** 账号已禁用 */
    ACCOUNT_DISABLED(614, "账号已禁用"),
    /** 用户名已存在 */
    USERNAME_ALREADY_EXISTS(615, "用户名已存在"),
    /** 邮箱已存在 */
    EMAIL_ALREADY_EXISTS(616, "邮箱已存在"),
    /** 无效的凭证 */
    INVALID_CREDENTIALS(617, "无效的凭证"),
    /** 用户名必填 */
    USERNAME_REQUIRED(618, "用户名必填"),
    /** 密码必填 */
    PASSWORD_REQUIRED(619, "密码必填"),

    // ========== 权限相关错误码 (620-629) ==========
    /** 没有权限 */
    NO_PERMISSION(620, "没有权限"),
    /** 令牌过期 */
    TOKEN_EXPIRED(621, "令牌过期"),
    /** 无效令牌 */
    INVALID_TOKEN(622, "无效令牌"),

    // ========== 业务操作错误码 (630-639) ==========
    /** 操作失败 */
    OPERATION_FAILED(630, "操作失败"),
    /** 数据不存在 */
    DATA_NOT_FOUND(631, "数据不存在"),
    /** 状态错误 */
    STATUS_ERROR(632, "状态错误"),
    /** 注册失败 */
    REGISTRATION_FAILED(633, "注册失败"),
    /** 更新失败 */
    UPDATE_FAILED(634, "更新失败"),
    /** 删除失败 */
    DELETE_FAILED(635, "删除失败"),
    /** 邮箱必填 */
    EMAIL_REQUIRED(636, "邮箱必填");

    private final int code;
    private final String message;

    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取消息
     *
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ResponseCode getByCode(int code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode() == code) {
                return responseCode;
            }
        }
        return null;
    }
}

package com.example.pure.common;

import com.example.pure.constant.ResponseCode;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Result class test
 * Verify ResponseCode enum integration works properly
 */
public class ResultTest {

    @Test
    public void testSuccessWithEnum() {
        // Test success response using enum
        Result<Void> result = Result.success();

        assertEquals(ResponseCode.SUCCESS.getCode(), result.getCode());
        assertTrue(result.getSuccess());
        assertNotNull(result.getTime());
    }

    @Test
    public void testSuccessWithData() {
        // Test success response with data
        String testData = "test data";
        Result<String> result = Result.success(testData);

        assertEquals(ResponseCode.SUCCESS.getCode(), result.getCode());
        assertEquals(testData, result.getData());
        assertTrue(result.getSuccess());
    }

    @Test
    public void testErrorWithEnum() {
        // Test error response using enum
        Result<Void> result = Result.error(ResponseCode.INVALID_PARAMETER);

        assertEquals(ResponseCode.INVALID_PARAMETER.getCode(), result.getCode());
        assertEquals(ResponseCode.INVALID_PARAMETER.getMessage(), result.getMessage());
        assertFalse(result.getSuccess());
    }

    @Test
    public void testErrorWithEnumAndCustomMessage() {
        // Test error response using enum with custom message
        String customMessage = "Custom error message";
        Result<Void> result = Result.error(ResponseCode.UNAUTHORIZED, customMessage);

        assertEquals(ResponseCode.UNAUTHORIZED.getCode(), result.getCode());
        assertEquals(customMessage, result.getMessage());
        assertFalse(result.getSuccess());
    }

    @Test
    public void testErrorWithEnumAndData() {
        // Test error response using enum with data
        String errorData = "error details";
        Result<String> result = Result.error(ResponseCode.BUSINESS_ERROR, errorData);

        assertEquals(ResponseCode.BUSINESS_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCode.BUSINESS_ERROR.getMessage(), result.getMessage());
        assertEquals(errorData, result.getData());
        assertFalse(result.getSuccess());
    }

    @Test
    public void testLegacyErrorMethods() {
        // Test legacy error methods still work
        Result<Void> result = Result.error(400, "Bad Request");

        assertEquals(400, result.getCode());
        assertEquals("Bad Request", result.getMessage());
        assertFalse(result.getSuccess());
    }

    @Test
    public void testResponseCodeEnum() {
        // Test ResponseCode enum itself
        assertEquals(200, ResponseCode.SUCCESS.getCode());
        assertEquals("操作成功", ResponseCode.SUCCESS.getMessage());

        assertEquals(401, ResponseCode.UNAUTHORIZED.getCode());
        assertEquals("未认证", ResponseCode.UNAUTHORIZED.getMessage());

        assertEquals(601, ResponseCode.INVALID_PARAMETER.getCode());
        assertEquals("参数验证错误", ResponseCode.INVALID_PARAMETER.getMessage());
    }

    @Test
    public void testResponseCodeGetByCode() {
        // Test get enum by status code
        assertEquals(ResponseCode.SUCCESS, ResponseCode.getByCode(200));
        assertEquals(ResponseCode.UNAUTHORIZED, ResponseCode.getByCode(401));
        assertEquals(ResponseCode.INVALID_PARAMETER, ResponseCode.getByCode(601));

        // Test non-existent status code
        assertNull(ResponseCode.getByCode(999));
    }
}

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;

/**
 * Simple verification script to test our changes
 */
public class VerifyChanges {
    public static void main(String[] args) {
        System.out.println("=== Verifying ResponseCode Enum Changes ===");
        
        // Test ResponseCode enum
        System.out.println("1. Testing ResponseCode enum:");
        System.out.println("   SUCCESS code: " + ResponseCode.SUCCESS.getCode());
        System.out.println("   SUCCESS message: " + ResponseCode.SUCCESS.getMessage());
        System.out.println("   UNAUTHORIZED code: " + ResponseCode.UNAUTHORIZED.getCode());
        System.out.println("   UNAUTHORIZED message: " + ResponseCode.UNAUTHORIZED.getMessage());
        
        // Test Result success methods (using enum internally)
        System.out.println("\n2. Testing Result success methods:");
        Result<Void> successResult = Result.success();
        System.out.println("   Success result code: " + successResult.getCode());
        System.out.println("   Success result message: " + successResult.getMessage());
        System.out.println("   Success result success: " + successResult.getSuccess());
        
        Result<String> successWithData = Result.success("test data");
        System.out.println("   Success with data code: " + successWithData.getCode());
        System.out.println("   Success with data: " + successWithData.getData());
        
        // Test Result error methods with enum
        System.out.println("\n3. Testing Result error methods with enum:");
        Result<Void> errorResult = Result.error(ResponseCode.INVALID_PARAMETER);
        System.out.println("   Error result code: " + errorResult.getCode());
        System.out.println("   Error result message: " + errorResult.getMessage());
        System.out.println("   Error result success: " + errorResult.getSuccess());
        
        Result<Void> errorWithCustomMessage = Result.error(ResponseCode.UNAUTHORIZED, "Custom message");
        System.out.println("   Error with custom message code: " + errorWithCustomMessage.getCode());
        System.out.println("   Error with custom message: " + errorWithCustomMessage.getMessage());
        
        // Test legacy error methods still work
        System.out.println("\n4. Testing legacy error methods:");
        Result<Void> legacyError = Result.error(400, "Bad Request");
        System.out.println("   Legacy error code: " + legacyError.getCode());
        System.out.println("   Legacy error message: " + legacyError.getMessage());
        
        // Test getByCode method
        System.out.println("\n5. Testing getByCode method:");
        ResponseCode foundCode = ResponseCode.getByCode(200);
        System.out.println("   Found code for 200: " + (foundCode != null ? foundCode.name() : "null"));
        
        ResponseCode notFoundCode = ResponseCode.getByCode(999);
        System.out.println("   Found code for 999: " + (notFoundCode != null ? notFoundCode.name() : "null"));
        
        System.out.println("\n=== All tests completed successfully! ===");
    }
}
